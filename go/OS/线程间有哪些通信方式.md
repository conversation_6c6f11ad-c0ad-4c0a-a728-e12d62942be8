# 线程间通信方式

线程间通信是多线程程序中线程之间进行数据交换和协调操作的机制。

## 主要通信方式

### 1. 共享内存
- **概念**：多个线程共享相同的内存区域，通过读写共享变量进行通信
- **同步问题**：需要使用锁（如互斥锁）保护共享数据，避免竞态条件
- **优点**：通信效率高，直接访问内存
- **缺点**：需要复杂的同步机制

### 2. 信号量（Semaphore）
- **概念**：计数器，用于控制对共享资源的访问
- **操作**：P操作（wait）减少计数，V操作（signal）增加计数
- **应用**：控制有限资源访问，实现线程同步

### 3. 条件变量（Condition Variable）
- **概念**：实现复杂的等待和通知机制
- **功能**：线程等待特定条件，条件满足时通知等待线程
- **应用**：生产者-消费者模型，事件通知

### 4. 消息队列（Message Queue）
- **概念**：线程间传递数据的队列结构
- **特点**：FIFO模式，解耦线程间直接依赖
- **应用**：生产者-消费者模式，任务分发

### 5. 管道（Pipe）
- **概念**：半双工通信机制，数据单向流动
- **特点**：适用于流式数据传输
- **应用**：数据流处理，进程间通信

### 6. 事件（Event）
- **概念**：线程间同步的信号机制
- **操作**：设置事件状态，等待事件发生
- **应用**：条件同步，状态通知

### 7. 锁机制
- **互斥锁（Mutex）**：保护共享资源，确保独占访问
- **读写锁（Read-Write Lock）**：允许多读单写
- **自旋锁（Spinlock）**：忙等待，适用于短时间锁定

### 8. 内存屏障（Memory Barrier）
- **概念**：强制内存操作顺序的指令
- **作用**：确保多线程环境下的内存访问顺序
- **应用**：防止编译器和CPU优化导致的竞态问题

### 9. 线程本地存储（TLS）
- **概念**：每个线程独立的存储空间
- **特点**：线程间不共享TLS数据
- **应用**：维护线程独立状态

## 选择原则

| 通信方式 | 适用场景 | 优点 | 缺点 |
|---------|----------|------|------|
| 共享内存 | 高频数据交换 | 效率高 | 同步复杂 |
| 消息队列 | 任务分发 | 解耦性好 | 有拷贝开销 |
| 信号量 | 资源控制 | 简单有效 | 功能单一 |
| 条件变量 | 复杂同步 | 灵活性强 | 使用复杂 |

## 面试要点

1. **共享内存vs消息传递**：效率vs安全性的权衡
2. **死锁预防**：避免循环等待，使用超时机制
3. **性能考虑**：根据场景选择合适的通信方式
4. **同步原语**：理解各种锁的使用场景和性能特点