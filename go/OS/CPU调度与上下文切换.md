# CPU调度与上下文切换

## CPU调度基础

### 1. 调度的基本概念
CPU调度是操作系统内核的核心功能，负责决定在多个可运行进程中选择哪个进程来使用CPU。

#### 调度时机
- **进程阻塞**：等待I/O操作完成
- **进程终止**：进程执行完毕或被终止
- **时间片用完**：抢占式调度中时间片到期
- **高优先级进程就绪**：更高优先级进程变为可运行状态

#### 调度目标
- **公平性**：所有进程都能获得合理的CPU时间
- **效率**：最大化CPU利用率
- **响应时间**：最小化用户感知的延迟
- **吞吐量**：单位时间内完成的任务数量

### 2. 进程状态转换
```
[新建] → [就绪] → [运行] → [终止]
           ↑        ↓
           └─[阻塞]─┘
```

## 经典调度算法

### 1. 先来先服务（FCFS）
- **原理**：按进程到达顺序执行
- **优点**：实现简单，公平
- **缺点**：平均等待时间长，不适合交互式系统

### 2. 最短作业优先（SJF）
- **原理**：优先执行执行时间最短的进程
- **优点**：平均等待时间最短
- **缺点**：可能导致长作业饥饿，需要预知执行时间

### 3. 轮转调度（RR）
- **原理**：每个进程分配固定时间片，轮流执行
- **优点**：响应时间好，适合交互式系统
- **缺点**：上下文切换开销大

### 4. 多级反馈队列
- **原理**：多个优先级队列，进程根据行为在队列间移动
- **优点**：兼顾响应时间和吞吐量，适应不同类型进程
- **缺点**：实现复杂，参数调优困难

### 5. 调度算法对比

| 算法 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| FCFS | 简单公平 | 等待时间长 | 批处理系统 |
| SJF | 等待时间短 | 长作业饥饿 | 已知执行时间 |
| RR | 响应时间好 | 切换开销大 | 交互式系统 |
| 多级反馈 | 自适应性强 | 实现复杂 | 通用系统 |

## 上下文切换

### 1. 上下文切换的概念
上下文切换是指CPU从一个进程切换到另一个进程时，保存当前进程的状态并恢复目标进程状态的过程。

#### 进程上下文包含：
- **CPU寄存器状态**：通用寄存器、程序计数器、状态寄存器
- **内存管理信息**：页表、段表
- **文件描述符表**
- **信号处理信息**
- **进程优先级和调度信息**

### 2. 上下文切换步骤
1. **保存当前进程状态**：寄存器、程序计数器等
2. **切换内存空间**：更新页表、刷新TLB
3. **切换内核栈**：更新栈指针
4. **恢复新进程状态**：加载寄存器、程序计数器

### 3. 上下文切换的开销

#### 直接开销
- **寄存器保存/恢复**：几十个CPU周期
- **内存管理单元切换**：刷新TLB，几百个周期
- **缓存失效**：L1/L2/L3缓存失效，数千个周期

#### 间接开销
- **缓存局部性丢失**：新进程的数据不在缓存中
- **分支预测失效**：CPU分支预测器需要重新学习
- **内存预取失效**：内存预取机制需要重新适应

### 4. 减少上下文切换开销的方法
- **使用线程**：共享地址空间，减少内存切换开销
- **使用协程**：用户态切换，开销最小
- **CPU亲和性**：减少进程在CPU间迁移
- **批处理**：减少切换频率
- **异步I/O**：避免阻塞导致的切换

## 现代调度器

### 1. Linux CFS调度器
- **原理**：完全公平调度器，基于虚拟运行时间
- **数据结构**：红黑树存储进程，按vruntime排序
- **选择策略**：总是选择vruntime最小的进程
- **特点**：保证长期公平性，适合交互式任务

### 2. 实时调度
- **SCHED_FIFO**：先进先出，同优先级按顺序执行
- **SCHED_RR**：轮转调度，同优先级轮流执行
- **SCHED_DEADLINE**：截止时间调度，保证时间约束

## 性能分析和优化

### 1. 性能分析工具
- **perf**：分析调度延迟和上下文切换
- **top/htop**：查看CPU使用率和进程状态
- **vmstat**：查看系统整体性能指标

### 2. 优化策略
- **CPU亲和性**：绑定进程到特定CPU
- **NUMA优化**：考虑内存访问局部性
- **负载均衡**：合理分配任务到不同CPU

## 面试要点

### 核心概念
1. **调度算法**：FCFS、SJF、RR、多级反馈队列
2. **上下文切换**：过程、开销、优化方法
3. **现代调度器**：CFS、实时调度
4. **性能优化**：减少切换开销的方法

### 常见问题
- **调度算法比较**：各算法的优缺点和适用场景
- **上下文切换开销**：直接开销vs间接开销
- **线程vs进程**：切换开销的差异
- **实时调度**：如何保证时间约束

### 优化技巧
1. **选择合适的调度策略**：根据应用特性选择
2. **减少上下文切换**：使用线程、协程、异步I/O
3. **CPU亲和性设置**：减少缓存失效
4. **负载均衡**：避免CPU热点

## 总结

CPU调度和上下文切换是操作系统性能的关键因素，理解其原理和优化方法对系统编程至关重要。
