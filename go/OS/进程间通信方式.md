# 进程间通信方式（IPC）

进程间通信（Inter-Process Communication, IPC）是不同进程之间进行数据交换和信号传递的机制。

## 主要IPC方式

### 1. 管道（Pipe）
- **匿名管道**：用于有亲缘关系的进程，单向数据流
- **命名管道（FIFO）**：可用于无亲缘关系的进程，通过文件系统路径访问
- **特点**：简单易用，但只能单向通信

### 2. 消息队列（Message Queue）
- **概念**：内核维护的消息链表，支持消息类型
- **特点**：保证消息有序性，支持非阻塞操作
- **优点**：解耦进程，支持优先级

### 3. 共享内存（Shared Memory）
- **概念**：多个进程共享同一块物理内存
- **特点**：最快的IPC方式，需要同步机制
- **应用**：大数据量传输，高性能要求

### 4. 信号量（Semaphore）
- **概念**：控制共享资源访问的计数器
- **操作**：P操作（wait）和V操作（signal）
- **应用**：配合共享内存使用，实现同步

### 5. 信号（Signal）
- **概念**：异步通知机制，软件中断
- **特点**：轻量级，用于简单事件通知
- **应用**：进程控制，异常处理

### 6. 套接字（Socket）
- **本地套接字**：同一主机进程通信
- **网络套接字**：跨主机进程通信
- **类型**：TCP（可靠）和UDP（快速）

### 7. 内存映射文件（mmap）
- **概念**：将文件映射到进程地址空间
- **特点**：多进程可共享同一文件内容
- **优点**：高效的文件I/O

## IPC方式对比

| 方式 | 速度 | 使用难度 | 数据量 | 适用场景 |
|------|------|----------|--------|----------|
| 共享内存 | 最快 | 复杂 | 大 | 高性能数据交换 |
| 消息队列 | 中等 | 简单 | 中等 | 任务分发 |
| 管道 | 中等 | 简单 | 小 | 简单数据传输 |
| 套接字 | 较慢 | 中等 | 任意 | 网络通信 |
| 信号 | 快 | 简单 | 无 | 事件通知 |

## 选择原则

1. **数据量大**：选择共享内存
2. **跨网络**：选择套接字
3. **简单通知**：选择信号
4. **任务队列**：选择消息队列
5. **父子进程**：选择管道

## 面试要点

1. **共享内存vs消息队列**：速度vs安全性
2. **同步问题**：共享内存需要额外同步机制
3. **性能考虑**：根据数据量和频率选择
4. **跨平台性**：不同系统支持的IPC方式有差异