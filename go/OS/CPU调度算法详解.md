# CPU调度算法详解

## 基本概念

### CPU调度
CPU调度是操作系统从就绪队列中选择一个进程分配CPU的过程，是操作系统的核心功能之一。

### 调度时机
- **进程从运行态变为等待态**：I/O请求、等待子进程
- **进程从运行态变为就绪态**：时间片到期、高优先级进程到达
- **进程从等待态变为就绪态**：I/O完成
- **进程终止**

## 调度算法分类

### 抢占式 vs 非抢占式
- **抢占式**：可以强制收回CPU
- **非抢占式**：进程主动放弃CPU才能调度

### 批处理 vs 交互式 vs 实时
- **批处理**：吞吐量优先
- **交互式**：响应时间优先
- **实时**：截止时间优先

## 主要调度算法

### 1. 先来先服务（FCFS）

#### 算法原理
- 按进程到达顺序进行调度
- 非抢占式算法
- 使用FIFO队列实现

#### 优缺点
- **优点**：简单公平，无饥饿问题
- **缺点**：平均等待时间长，护航效应
- **护航效应**：短进程等待长进程完成

#### 适用场景
- 批处理系统
- 进程执行时间相近的场景

### 2. 最短作业优先（SJF）

#### 算法原理
- 选择执行时间最短的进程
- 可以是抢占式（SRTF）或非抢占式
- 理论上平均等待时间最短

#### 优缺点
- **优点**：平均等待时间最短
- **缺点**：长进程可能饥饿，难以预测执行时间
- **饥饿问题**：长进程可能永远得不到执行

#### 实现难点
- **执行时间预测**：使用历史数据估算
- **指数平均法**：τ(n+1) = α×t(n) + (1-α)×τ(n)

### 3. 优先级调度

#### 算法原理
- 为每个进程分配优先级
- 选择优先级最高的进程执行
- 可以是静态或动态优先级

#### 优先级设置
- **静态优先级**：创建时确定，不再改变
- **动态优先级**：根据进程行为动态调整
- **优先级反转**：低优先级进程阻塞高优先级进程

#### 解决饥饿问题
- **老化技术**：随时间增加进程优先级
- **多级反馈**：结合时间片轮转

### 4. 时间片轮转（RR）

#### 算法原理
- 每个进程分配固定时间片
- 时间片用完后切换到下一个进程
- 抢占式算法，适合交互式系统

#### 时间片选择
- **太小**：上下文切换开销大
- **太大**：退化为FCFS
- **经验值**：10-100毫秒

#### 优缺点
- **优点**：响应时间好，公平
- **缺点**：上下文切换开销
- **适用**：分时系统、交互式系统

### 5. 多级队列调度

#### 算法原理
- 将就绪队列分为多个子队列
- 不同队列使用不同调度算法
- 队列间有固定优先级关系

#### 队列分类
- **系统进程队列**：最高优先级
- **交互式进程队列**：高优先级，时间片轮转
- **批处理进程队列**：低优先级，FCFS

#### 优缺点
- **优点**：灵活，适应不同类型进程
- **缺点**：可能导致饥饿

### 6. 多级反馈队列调度

#### 算法原理
- 多个优先级不同的队列
- 进程可以在队列间移动
- 新进程进入最高优先级队列

#### 调度规则
1. 优先调度高优先级队列中的进程
2. 同优先级队列内使用时间片轮转
3. 进程用完时间片降到下一级队列
4. I/O密集型进程优先级提升

#### 优点
- **自适应**：自动区分I/O密集型和CPU密集型
- **响应时间好**：短进程快速完成
- **无需预知执行时间**

## 实时调度算法

### 1. 速率单调调度（RM）

#### 算法原理
- 静态优先级分配
- 周期越短，优先级越高
- 适用于周期性实时任务

#### 可调度条件
- n个任务的CPU利用率 ≤ n(2^(1/n) - 1)
- 当n→∞时，上界约为69.3%

### 2. 最早截止时间优先（EDF）

#### 算法原理
- 动态优先级分配
- 截止时间越早，优先级越高
- 理论上最优的实时调度算法

#### 优点
- **最优性**：能调度的任务集合最大
- **CPU利用率**：可达100%

## 调度算法性能指标

### 主要指标
- **CPU利用率**：CPU忙碌时间比例
- **吞吐量**：单位时间完成的进程数
- **周转时间**：进程完成时间-到达时间
- **等待时间**：进程在就绪队列中的等待时间
- **响应时间**：从请求到首次响应的时间

### 优化目标
- **批处理系统**：最大化吞吐量和CPU利用率
- **交互式系统**：最小化响应时间
- **实时系统**：满足截止时间要求

## Linux CFS调度器

### 完全公平调度器（CFS）
- **虚拟运行时间**：vruntime概念
- **红黑树**：维护就绪进程
- **公平性**：每个进程获得相等的CPU时间

### 关键特性
- **O(log n)复杂度**：红黑树操作
- **负载均衡**：多核CPU间的任务迁移
- **组调度**：支持进程组调度

## 面试要点

### 算法对比

| 算法 | 类型 | 优点 | 缺点 | 适用场景 |
|------|------|------|------|----------|
| FCFS | 非抢占 | 简单公平 | 护航效应 | 批处理 |
| SJF | 非抢占 | 等待时间短 | 饥饿问题 | 批处理 |
| RR | 抢占 | 响应时间好 | 切换开销 | 分时系统 |
| 优先级 | 抢占/非抢占 | 灵活 | 饥饿问题 | 实时系统 |
| 多级反馈 | 抢占 | 自适应 | 复杂 | 通用系统 |

### 高频问题
1. **时间片大小如何选择？**
   - 平衡响应时间和切换开销，通常10-100ms
2. **如何解决优先级调度的饥饿问题？**
   - 老化技术，随时间提高优先级
3. **多级反馈队列的优势？**
   - 自适应区分进程类型，无需预知执行时间
4. **实时调度和普通调度的区别？**
   - 实时调度关注截止时间，普通调度关注平均性能
5. **Linux使用什么调度算法？**
   - CFS完全公平调度器，基于虚拟运行时间

### 实践要点
- **性能调优**：根据应用特点选择合适算法
- **多核调度**：考虑负载均衡和缓存局部性
- **实时性要求**：使用实时调度算法
- **公平性保证**：防止进程饥饿
