# 死锁问题详解

## 基本概念

### 死锁定义
死锁是指两个或多个进程在执行过程中，因争夺资源而造成的一种互相等待的现象，若无外力作用，它们都将无法推进下去。

## 死锁产生的四个必要条件

### 1. 互斥条件（Mutual Exclusion）
- 资源不能被多个进程同时使用
- 一次只有一个进程能使用资源

### 2. 占有和等待条件（Hold and Wait）
- 进程已经保持至少一个资源
- 同时又在请求其他被别的进程占有的资源

### 3. 不可剥夺条件（No Preemption）
- 资源不能被强制性地从进程中剥夺
- 只能由占有它的进程主动释放

### 4. 循环等待条件（Circular Wait）
- 存在一个进程资源的循环链
- 每个进程都在等待下一个进程所占有的资源

## 死锁预防

### 破坏互斥条件
- **方法**：允许资源共享
- **适用**：只对某些资源有效（如只读文件）
- **局限**：很多资源本质上不可共享

### 破坏占有和等待条件
- **方法1**：进程开始前一次性申请所有资源
- **方法2**：进程申请新资源前必须释放已占有的资源
- **缺点**：资源利用率低，可能导致饥饿

### 破坏不可剥夺条件
- **方法**：允许系统强制回收资源
- **适用**：状态易保存和恢复的资源
- **实现**：虚拟化技术

### 破坏循环等待条件
- **方法**：对资源进行线性排序，按序申请
- **优点**：实现简单，开销小
- **缺点**：限制了程序的并发性

## 死锁避免

### 银行家算法
- **原理**：在分配资源前判断是否会导致不安全状态
- **核心**：保证系统始终处于安全状态
- **数据结构**：
  - Available：可用资源向量
  - Max：最大需求矩阵
  - Allocation：分配矩阵
  - Need：需求矩阵

### 安全状态判断
1. 寻找Need[i] ≤ Available的进程i
2. 假设进程i完成，回收其资源
3. 重复步骤1-2，直到所有进程完成
4. 如果能找到安全序列，则系统处于安全状态

## 死锁检测

### 资源分配图
- **节点**：进程节点和资源节点
- **边**：请求边和分配边
- **检测**：图中是否存在环

### 检测算法
1. 初始化工作向量Work = Available
2. 寻找满足条件的进程：Request[i] ≤ Work
3. 模拟进程完成，释放资源：Work = Work + Allocation[i]
4. 重复2-3步，如果不能完成所有进程，则存在死锁

## 死锁恢复

### 进程终止
- **终止所有死锁进程**：简单但代价大
- **逐个终止死锁进程**：每次终止一个，检查死锁是否解除

### 资源抢占
- **选择受害者**：最小化代价
- **回滚**：回到安全状态
- **饥饿**：防止同一进程总是被选为受害者

## 实际应用中的死锁

### 数据库死锁
- **场景**：多个事务同时访问多个资源
- **检测**：等待图检测
- **解决**：事务回滚

### 操作系统死锁
- **文件锁**：多个进程锁定多个文件
- **内存分配**：内存不足时的资源竞争
- **设备分配**：打印机、磁带机等独占设备

### 多线程死锁
- **互斥锁**：多个线程以不同顺序获取锁
- **条件变量**：等待条件时持有锁
- **读写锁**：读写锁的升级降级

## 死锁预防的编程实践

### 锁排序
```
// 总是按相同顺序获取锁
if (lock1_id < lock2_id) {
    acquire(lock1);
    acquire(lock2);
} else {
    acquire(lock2);
    acquire(lock1);
}
```

### 超时机制
- 设置获取锁的超时时间
- 超时后释放已获得的锁
- 避免无限等待

### 锁粒度控制
- 减少锁的持有时间
- 使用细粒度锁
- 避免嵌套锁

## 面试要点

### 核心概念
1. **四个必要条件**：互斥、占有等待、不可剥夺、循环等待
2. **处理策略**：预防、避免、检测、恢复
3. **银行家算法**：安全状态判断
4. **实际应用**：数据库、操作系统、多线程

### 常见问题
1. **如何避免死锁？**
   - 锁排序、超时机制、减少锁粒度
2. **银行家算法的核心思想？**
   - 保证系统始终处于安全状态
3. **死锁检测的方法？**
   - 资源分配图、等待图检测
4. **实际项目中遇到的死锁问题？**
   - 数据库事务死锁、多线程锁竞争
